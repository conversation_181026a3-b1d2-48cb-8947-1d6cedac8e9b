package com.codewithaatif.store;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;

@SpringBootApplication
public class StoreApplication {

	public static void main(String[] args) {

		ApplicationContext context = SpringApplication.run(StoreApplication.class, args);

		var orderService = context.getBean(OrderService.class);
		orderService.placeOrder();

		var notificationService = context.getBean(NotificationManager.class);
		notificationService.sendNotification();

		var userService = context.getBean(UserService.class);
		var user1 = new User(1L, "<EMAIL>", "test1", "Test1");
		var user2 = new User(1L, "<EMAIL>", "test2", "Test2");
		userService.registerUser(user1);
		userService.registerUser(user2);
		// var orderService = new OrderService(new PayPalPaymentService());

		// var resource = context.getBean(HeavyResource.class);
		
	}

}
