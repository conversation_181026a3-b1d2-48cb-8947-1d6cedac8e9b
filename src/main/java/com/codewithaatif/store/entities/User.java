package com.codewithaatif.store.entities;

import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;


    @Column(nullable=false, name = "email")
    private String email;


    @Column(nullable=false, name = "password")
    private String password;

    

    @Column(nullable=false, name = "name")
    private String name;

    @OneToMany(mappedBy = "user")
    private List<Address> addresses;

    public void addAddress(Address address) {
        addresses.add(address);
        address.setUser(this);
    }
    
}