
package com.codewithaatif.store;

import org.springframework.stereotype.Service;

@Service("stripePaymentService")
public class StripePaymentService implements PaymentService {

    @Value("${stripe.apiUrl}")
    private String apiUrl;

    @Value("${stripe.enabled}")
    private boolean enabled;

    @Value("${stripe.supported-currencies}")
    private List<String> supportedCurrencies;

    @Value("${stripe.timeout:3000}")
    private int timeout; // if not provided it will use default value 3000 supportedCurrencies;
    
    @Override
    public void processPayment(double amount) {
        System.out.println("API URL: " + apiUrl);
        System.out.println("Enabled: " + enabled);
        System.out.println("Supported Currencies: " + supportedCurrencies);
        System.out.println("Timeout: " + timeout);
        System.out.println("Payment processed from Stripe:"+ amount);
    }
}
