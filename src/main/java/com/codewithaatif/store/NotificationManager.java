
package com.codewithaatif.store;

public class NotificationManager {

    private NotificationService notificationService;

    public NotificationManager(NotificationService notificationService) {
        System.out.println("Notification Service created");
        this.notificationService = notificationService;
    }

    public void sendNotification() {
        notificationService.send("Hello world");
    }
}